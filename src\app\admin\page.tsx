'use client';

import { useState, useEffect } from 'react';
import { apiClient } from '@/lib/api';
import { AITool, DbTool, AICategory, ContentStatus } from '@/lib/types';

export default function AdminPanel() {
  const [tools, setTools] = useState<DbTool[]>([]);
  const [categories, setCategories] = useState<AICategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTool, setSelectedTool] = useState<DbTool | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const [toolsResult, categoriesResult] = await Promise.all([
        apiClient.getTools({ limit: 100 }),
        apiClient.getCategories()
      ]);
      // Transform AITool[] to DbTool[] for admin interface
      const dbTools: DbTool[] = toolsResult.data.map(tool => ({
        id: tool.id,
        name: tool.name,
        slug: tool.slug || '',
        logo_url: tool.logoUrl,
        description: tool.description,
        short_description: tool.shortDescription,
        detailed_description: tool.detailedDescription,
        link: tool.link,
        website: tool.website,
        category_id: tool.category,
        subcategory: tool.subcategory,
        company: tool.company,
        is_verified: tool.isVerified,
        is_claimed: tool.isClaimed,
        content_status: tool.contentStatus,
        created_at: tool.createdAt,
        updated_at: tool.updatedAt,
        published_at: tool.publishedAt,
        // Enhanced AI System fields
        scraped_data: tool.scrapedData,
        ai_generation_status: tool.aiGenerationStatus,
        last_scraped_at: tool.lastScrapedAt,
        editorial_review_id: tool.editorialReviewId,
        ai_generation_job_id: tool.aiGenerationJobId,
        submission_type: tool.submissionType,
        submission_source: tool.submissionSource,
        content_quality_score: tool.contentQualityScore,
        last_ai_update: tool.lastAiUpdate,
      }));
      setTools(dbTools);
      setCategories(categoriesResult);
    } catch (error) {
      console.error('Failed to load data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteTool = async (toolId: string) => {
    if (!confirm('Are you sure you want to delete this tool?')) return;
    
    try {
      await apiClient.deleteTool(toolId, 'aidude_admin_2024_secure_key_xyz789');
      setTools(tools.filter(t => t.id !== toolId));
      alert('Tool deleted successfully!');
    } catch (error) {
      alert('Failed to delete tool: ' + (error as Error).message);
    }
  };

  const handleUpdateStatus = async (toolId: string, status: ContentStatus) => {
    try {
      await apiClient.updateTool(toolId, { contentStatus: status }, 'aidude_admin_2024_secure_key_xyz789');
      setTools(tools.map(t => t.id === toolId ? { ...t, content_status: status } : t));
      alert('Tool status updated!');
    } catch (error) {
      alert('Failed to update status: ' + (error as Error).message);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-zinc-900 flex items-center justify-center">
        <div className="text-white text-xl">Loading admin panel...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-zinc-900 text-white">
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold">AI Dude Admin Panel</h1>
          <div className="flex space-x-3">
            <a
              href="/admin/jobs"
              className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg font-medium transition-colors"
            >
              📊 Job Monitor
            </a>
            <a
              href="/admin/bulk"
              className="bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded-lg font-medium transition-colors"
            >
              📦 Bulk Processing
            </a>
            <a
              href="/admin/settings"
              className="bg-zinc-700 hover:bg-zinc-600 px-4 py-2 rounded-lg font-medium transition-colors"
            >
              ⚙️ Settings
            </a>
            <button
              onClick={() => setShowAddForm(true)}
              className="bg-orange-500 hover:bg-orange-600 px-4 py-2 rounded-lg font-medium"
            >
              Add New Tool
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-zinc-800 p-6 rounded-lg border border-zinc-700">
            <h3 className="text-lg font-semibold mb-2">Total Tools</h3>
            <p className="text-3xl font-bold text-orange-500">{tools.length}</p>
          </div>
          <div className="bg-zinc-800 p-6 rounded-lg border border-zinc-700">
            <h3 className="text-lg font-semibold mb-2">Published</h3>
            <p className="text-3xl font-bold text-green-500">
              {tools.filter(t => t.content_status === 'published').length}
            </p>
          </div>
          <div className="bg-zinc-800 p-6 rounded-lg border border-zinc-700">
            <h3 className="text-lg font-semibold mb-2">Draft</h3>
            <p className="text-3xl font-bold text-yellow-500">
              {tools.filter(t => t.content_status === 'draft').length}
            </p>
          </div>
          <div className="bg-zinc-800 p-6 rounded-lg border border-zinc-700">
            <h3 className="text-lg font-semibold mb-2">Categories</h3>
            <p className="text-3xl font-bold text-blue-500">{categories.length}</p>
          </div>
        </div>

        {/* Tools Table */}
        <div className="bg-zinc-800 rounded-lg border border-zinc-700 overflow-hidden">
          <div className="p-6 border-b border-zinc-700">
            <h2 className="text-xl font-semibold">Manage Tools</h2>
          </div>
          
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-zinc-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Tool
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Category
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Created
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-zinc-700">
                {tools.map((tool) => (
                  <tr key={tool.id} className="hover:bg-zinc-700/50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <img
                          src={tool.logo_url || '/placeholder-logo.png'}
                          alt={tool.name}
                          className="w-8 h-8 rounded mr-3"
                          onError={(e) => {
                            (e.target as HTMLImageElement).src = '/placeholder-logo.png';
                          }}
                        />
                        <div>
                          <div className="text-sm font-medium text-white">{tool.name}</div>
                          <div className="text-sm text-gray-400 truncate max-w-xs">
                            {tool.description}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm text-gray-300">{tool.category_id}</span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        tool.content_status === 'published' 
                          ? 'bg-green-100 text-green-800' 
                          : tool.content_status === 'draft'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {tool.content_status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                      {tool.created_at ? new Date(tool.created_at).toLocaleDateString() : 'N/A'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                      {tool.content_status === 'draft' && (
                        <button
                          onClick={() => handleUpdateStatus(tool.id, 'published')}
                          className="text-green-400 hover:text-green-300"
                        >
                          Publish
                        </button>
                      )}
                      {tool.content_status === 'published' && (
                        <button
                          onClick={() => handleUpdateStatus(tool.id, 'draft')}
                          className="text-yellow-400 hover:text-yellow-300"
                        >
                          Unpublish
                        </button>
                      )}
                      <button
                        onClick={() => setSelectedTool(tool)}
                        className="text-blue-400 hover:text-blue-300"
                      >
                        Edit
                      </button>
                      <button
                        onClick={() => handleDeleteTool(tool.id)}
                        className="text-red-400 hover:text-red-300"
                      >
                        Delete
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-zinc-800 p-6 rounded-lg border border-zinc-700">
            <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
            <div className="space-y-2">
              <button className="w-full text-left px-3 py-2 rounded bg-zinc-700 hover:bg-zinc-600 transition-colors">
                Export Tools Data
              </button>
              <button className="w-full text-left px-3 py-2 rounded bg-zinc-700 hover:bg-zinc-600 transition-colors">
                Import Tools
              </button>
              <button className="w-full text-left px-3 py-2 rounded bg-zinc-700 hover:bg-zinc-600 transition-colors">
                Bulk Update Status
              </button>
            </div>
          </div>

          <div className="bg-zinc-800 p-6 rounded-lg border border-zinc-700">
            <h3 className="text-lg font-semibold mb-4">Recent Activity</h3>
            <div className="space-y-2 text-sm text-gray-400">
              <p>• 5 new tools added today</p>
              <p>• 12 tools published this week</p>
              <p>• 3 pending submissions</p>
            </div>
          </div>

          <div className="bg-zinc-800 p-6 rounded-lg border border-zinc-700">
            <h3 className="text-lg font-semibold mb-4">System Status</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Database</span>
                <span className="text-green-400">✓ Online</span>
              </div>
              <div className="flex justify-between">
                <span>API</span>
                <span className="text-green-400">✓ Online</span>
              </div>
              <div className="flex justify-between">
                <span>n8n</span>
                <span className="text-green-400">✓ Running</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
